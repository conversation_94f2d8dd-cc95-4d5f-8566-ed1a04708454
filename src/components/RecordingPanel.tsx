import { formatRecordingTime } from '../utils/time';

interface RecordingPanelProps {
  recordingTime: number;
  isRecording: boolean;
  onStop: () => void;
  onTogglePause: () => void;
}

/**
 * 录制面板组件，显示录制时间和控制按钮
 */
export const RecordingPanel = ({ 
  recordingTime, 
  isRecording, 
  onStop, 
  onTogglePause 
}: RecordingPanelProps) => {
  return (
    <div className="flex items-center justify-between w-full space-x-2">
      {/* 录制时间显示 */}
      <div className="text-center">
        <div
          className="text-sm font-mono font-bold text-gray-800"
          style={{
            fontFamily: 'SF Mono, Monaco, Inconsolata, "Roboto Mono", Consolas, "Courier New", monospace',
          }}
        >
          {formatRecordingTime(recordingTime)}
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex items-center space-x-1">
        {/* 停止录制按钮 */}
        <button
          onClick={onStop}
          className="w-6 h-6 rounded-full bg-red-500 hover:bg-red-600 transition-all duration-200 flex items-center justify-center shadow-sm hover:shadow-md active:scale-95"
          title="停止录制"
        >
          <div className="w-2 h-2 bg-white rounded-sm"></div>
        </button>

        {/* 暂停/继续录制按钮 */}
        <button
          onClick={onTogglePause}
          className="w-6 h-6 rounded-full bg-gray-400 hover:bg-gray-500 transition-all duration-200 flex items-center justify-center shadow-sm hover:shadow-md active:scale-95"
          title={isRecording ? '暂停录制' : '继续录制'}
        >
          {isRecording ? (
            <div className="flex space-x-0.5">
              <div className="w-0.5 h-2 bg-white rounded-full"></div>
              <div className="w-0.5 h-2 bg-white rounded-full"></div>
            </div>
          ) : (
            <div className="w-0 h-0 border-l-2 border-l-white border-y-1 border-y-transparent ml-0.5"></div>
          )}
        </button>
      </div>
    </div>
  );
};
