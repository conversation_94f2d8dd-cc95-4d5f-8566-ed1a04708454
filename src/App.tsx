import { useState, useEffect } from 'react';
import { FloatingWindow, TaskInput, RecordingPanel, ActionPanel, ActionLog } from './components';
import { useRecording, useWindowVisibility, useRecordingActions } from './hooks';
import backgroundImage from './assets/background.jpg';
import chromeIcon from './assets/chrome.svg';

// 模拟桌面的步骤类型
type SimulationStep =
  | 'desktop'
  | 'chrome-opening'
  | 'chrome-loaded'
  | 'typing-url'
  | 'loading-jd'
  | 'jd-loaded'
  | 'clicking-search'
  | 'typing-search'
  | 'search-results'
  | 'scrolling-page'
  | 'clicking-product'
  | 'product-page'
  | 'copying-title';

function App() {
  const [inputValue, setInputValue] = useState('');

  // 使用自定义hooks管理状态
  const { isVisible, hideWindow, showWindow } = useWindowVisibility();
  const {
    isRecordingMode,
    isRecording,
    recordingTime,
    startRecording,
    stopRecording,
    togglePause
  } = useRecording();

  // 录制操作管理
  const { actions, clearActions } = useRecordingActions(isRecording);

  // 模拟桌面状态
  const [currentStep, setCurrentStep] = useState<SimulationStep>('desktop');
  const [typedUrl, setTypedUrl] = useState('');
  const [typedSearch, setTypedSearch] = useState('');
  const [showCursor, setShowCursor] = useState(true);
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [iframeError, setIframeError] = useState(false);
  const [copiedTitle, setCopiedTitle] = useState('');

  // 模拟打字效果
  const typeText = (text: string, setter: (value: string) => void, delay: number = 100) => {
    return new Promise<void>((resolve) => {
      let index = 0;
      const interval = setInterval(() => {
        if (index <= text.length) {
          setter(text.slice(0, index));
          index++;
        } else {
          clearInterval(interval);
          resolve();
        }
      }, delay);
    });
  };

  // 与iframe交互的函数
  const interactWithIframe = (callback: (iframeDocument: Document) => boolean): boolean => {
    try {
      const iframe = document.querySelector('iframe[title="京东网站"]') as HTMLIFrameElement;
      if (iframe && iframe.contentDocument) {
        return callback(iframe.contentDocument);
      } else {
        console.warn('无法访问iframe内容，可能由于跨域限制');
        return false;
      }
    } catch (error) {
      console.error('iframe交互失败:', error);
      return false;
    }
  };

  // 查找商品容器并点击第一个子元素
  const clickProductContainer = () => {
    const success = interactWithIframe((doc) => {
      // 查找匹配 _goodsContainer_*_plugin_goodsContainer 模式的div
      const allDivs = doc.querySelectorAll('div[class*="_goodsContainer_"][class*="_plugin_goodsContainer"]');
      console.log('🔍 Found product containers:', allDivs.length);

      if (allDivs.length > 0) {
        const firstContainer = allDivs[0];
        const firstChild = firstContainer.firstElementChild;

        if (firstChild) {
          console.log('🎯 Clicking first child of product container');
          (firstChild as HTMLElement).click();
          return true;
        } else {
          console.warn('商品容器没有子元素');
        }
      } else {
        console.warn('未找到匹配的商品容器');
      }
      return false;
    });

    if (!success) {
      console.log('🔄 Cannot access iframe due to cross-origin restrictions, simulating click');
      // 由于跨域限制，无法真实点击，但可以继续模拟流程
    }
  };

  // 复制商品标题
  const copyProductTitle = () => {
    const success = interactWithIframe((doc) => {
      // 尝试多种可能的商品标题选择器
      const titleSelectors = [
        'h1',
        '.sku-name',
        '.product-intro h1',
        '[data-hook="product_detail_title"]',
        '.p-name',
        '.itemInfo-wrap h1'
      ];

      let titleElement: Element | null = null;
      let titleText = '';

      for (const selector of titleSelectors) {
        titleElement = doc.querySelector(selector);
        if (titleElement) {
          titleText = titleElement.textContent?.trim() || '';
          if (titleText) {
            console.log('📝 Found title with selector:', selector);
            break;
          }
        }
      }

      if (titleText) {
        // 复制到剪贴板
        navigator.clipboard.writeText(titleText).then(() => {
          console.log('✅ Title copied to clipboard:', titleText);
          setCopiedTitle(titleText);
        }).catch((error) => {
          console.error('复制失败:', error);
          // 降级方案：使用传统的复制方法
          const textArea = document.createElement('textarea');
          textArea.value = titleText;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          setCopiedTitle(titleText);
        });
        return true;
      } else {
        console.warn('未找到商品标题');
        return false;
      }
    });

    // 如果无法访问iframe（跨域限制），使用模拟标题
    if (!success) {
      console.log('🔄 Using fallback title due to cross-origin restrictions');
      const fallbackTitle = 'Apple MacBook Pro 14英寸 M3芯片 8核CPU 10核GPU 8GB统一内存 512GB固态硬盘 深空灰色 笔记本电脑 MR7J3CH/A';
      setCopiedTitle(fallbackTitle);
      navigator.clipboard.writeText(fallbackTitle).catch(() => {
        console.log('Clipboard access denied, title stored locally');
      });
    }
  };

  // 光标闪烁效果
  useEffect(() => {
    const interval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);
    return () => clearInterval(interval);
  }, []);

  // 根据当前操作同步执行模拟
  useEffect(() => {
    if (!isRecordingMode || actions.length === 0) return;

    const currentAction = actions[actions.length - 1];
    if (!currentAction) return;

    const executeActionSimulation = async () => {
      const actionType = currentAction.type;
      const target = currentAction.target;

      // 提取目标元素的简化名称
      const getTargetName = (target: string): string => {
        const classIdMatch = target.match(/<class_id="([^"]+)">/);
        if (classIdMatch) {
          return classIdMatch[1];
        }
        // 保持原始大小写，但也提供小写版本用于比较
        return target;
      };

      const targetName = getTargetName(target);

      console.log('🎬 Executing simulation:', {
        actionType,
        target,
        targetName,
        currentStep
      });

      switch (actionType) {
        case 'Left Click':
          console.log('👆 Left Click on:', targetName);
          if (targetName === 'Chrome' || targetName === 'chrome') {
            console.log('🌐 Starting Chrome...');
            setCurrentStep('chrome-opening');
            await new Promise(resolve => setTimeout(resolve, 800));
            setCurrentStep('chrome-loaded');
            console.log('✅ Chrome loaded');
          } else if (targetName === 'address-bar') {
            console.log('📍 Clicking address bar');
            setCurrentStep('typing-url');
          } else if (targetName === 'search-input') {
            console.log('🔍 Clicking search input');
            setCurrentStep('clicking-search');
          } else if (targetName === 'search-button') {
            console.log('🔎 Clicking search button');
            // 重置iframe状态
            setIframeLoaded(false);
            setIframeError(false);
            setCurrentStep('search-results');
          } else if (targetName.includes('_goodsContainer_') && targetName.includes('_plugin_goodsContainer')) {
            console.log('🛍️ Clicking product container');
            setCurrentStep('clicking-product');
            // 等待一下确保页面稳定，然后真实点击
            await new Promise(resolve => setTimeout(resolve, 1000));
            clickProductContainer();
            await new Promise(resolve => setTimeout(resolve, 2000));
            setCurrentStep('product-page');
          }
          break;

        case 'Type':
          console.log('⌨️ Typing:', targetName);
          if (targetName === 'search.jd.com') {
            console.log('🌐 Typing URL: search.jd.com');
            await typeText('search.jd.com', setTypedUrl, 100);
          } else if (targetName === 'Mac电脑' || targetName === 'mac电脑') {
            console.log('🔍 Typing search: Mac电脑');
            await typeText('Mac电脑', setTypedSearch, 150);
          }
          console.log('✅ Typing completed');
          break;

        case 'Press':
          console.log('🔘 Pressing:', targetName);
          if (targetName === 'enter') {
            console.log('⏎ Pressing Enter, loading JD...');
            setCurrentStep('loading-jd');
            // 重置iframe状态
            setIframeLoaded(false);
            setIframeError(false);
            await new Promise(resolve => setTimeout(resolve, 1500));
            setCurrentStep('jd-loaded');
            console.log('✅ JD loaded');
          }
          break;

        case 'Scroll':
          console.log('📜 Scrolling:', targetName);
          if (targetName === 'Page') {
            console.log('⬇️ Scrolling down page');
            setCurrentStep('scrolling-page');
            await new Promise(resolve => setTimeout(resolve, 1000));
            // 滚动完成后回到搜索结果状态
            setCurrentStep('search-results');
            console.log('✅ Scrolling completed');
          }
          break;

        case 'Copy':
          console.log('📋 Copying:', targetName);
          if (targetName === 'Product Title') {
            console.log('📝 Copying product title');
            setCurrentStep('copying-title');
            // 真实地复制商品标题
            copyProductTitle();
            await new Promise(resolve => setTimeout(resolve, 2000));
            console.log('✅ Title copied to clipboard');
          }
          break;

        default:
          break;
      }
    };

    executeActionSimulation();
  }, [actions.length, isRecordingMode]);

  // 添加调试日志
  console.log('📱 App render:', {
    isRecordingMode,
    isRecording,
    actionsLength: actions.length,
    isVisible,
    shouldShowSimulation: isRecordingMode && actions.length > 0
  });

  // 事件处理函数
  const handleCameraClick = () => {
    startRecording();
  };

  const handleStopRecording = () => {
    stopRecording();
    clearActions();
  };

  const handleMicClick = () => {
    console.log('Microphone clicked');
  };

  const handleRunClick = () => {
    console.log('Run clicked!', inputValue);
  };

  const handleWindowClose = () => {
    hideWindow();
    setTimeout(() => showWindow(), 500);
  };

  const handleWindowMinimize = () => {
    console.log('Window minimize clicked');
  };

  const handleWindowMaximize = () => {
    console.log('Window maximize clicked');
  };

  return (
    <div
      className={`${isRecordingMode ? 'min-h-screen' : ''}`}
      style={isRecordingMode ? {
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      } : {}}
    >
      {/* Chrome图标 - 始终显示在桌面上 */}
      <div
        style={{
          position: 'fixed',
          bottom: '80px',
          left: '32px',
          padding: '10px',
          borderRadius: '8px',
          transform: currentStep === 'chrome-opening' ? 'scale(1.1)' : 'scale(1)',
          transition: 'transform 0.3s ease',
          border: currentStep === 'chrome-opening' ? '4px solid #3b82f6' : 'none',
          zIndex: 20
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
          <img
            src={chromeIcon}
            alt="Chrome"
            style={{
              width: '64px',
              height: '64px',
              cursor: 'pointer',
              transition: 'transform 0.2s ease'
            }}
          />
          <span style={{
            fontSize: '12px',
            color: '#374151',
            fontWeight: '500'
          }}>
            Chrome
          </span>
        </div>
      </div>

      {/* Chrome窗口 - 只在录制模式下且Chrome已启动时显示 */}
      {isRecordingMode && currentStep !== 'desktop' && currentStep !== 'chrome-opening' && (
        <div
          className="fixed inset-0 overflow-hidden z-30"
          style={{ position: 'fixed' }}
        >
          <div
            style={{
              position: 'absolute',
              top: '32px',
              left: '32px',
              right: '32px',
              bottom: '32px',
              backgroundColor: 'white',
              borderRadius: '8px',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
              transition: 'all 0.5s ease',
              border: '1px solid #e5e7eb'
            }}
          >
            {/* Chrome标题栏 */}
            <div style={{
              height: '40px',
              backgroundColor: '#f3f4f6',
              borderTopLeftRadius: '8px',
              borderTopRightRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              padding: '0 16px',
              borderBottom: '1px solid #e5e7eb'
            }}>
              <div style={{ display: 'flex', gap: '8px' }}>
                <div style={{ width: '12px', height: '12px', backgroundColor: '#ef4444', borderRadius: '50%' }} />
                <div style={{ width: '12px', height: '12px', backgroundColor: '#f59e0b', borderRadius: '50%' }} />
                <div style={{ width: '12px', height: '12px', backgroundColor: '#10b981', borderRadius: '50%' }} />
              </div>
              <div style={{ flex: 1, margin: '0 16px' }}>
                <div style={{
                  height: '24px',
                  backgroundColor: 'white',
                  borderRadius: '4px',
                  border: '1px solid #d1d5db',
                  padding: '0 12px',
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '14px',
                  color: currentStep === 'typing-url' ? '#1f2937' : '#9ca3af'
                }}>
                  {currentStep === 'chrome-loaded' && '搜索或输入网址'}
                  {(currentStep === 'typing-url' || currentStep === 'loading-jd' ||
                    currentStep === 'jd-loaded' || currentStep === 'clicking-search' ||
                    currentStep === 'typing-search' || currentStep === 'search-results') && (
                    <>
                      {typedUrl}
                      {currentStep === 'typing-url' && showCursor && '|'}
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Chrome内容区域 */}
            <div style={{ height: 'calc(100% - 40px)', overflow: 'hidden' }}>
              {/* 加载状态 */}
              {currentStep === 'loading-jd' && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%'
                }}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      border: '2px solid #3b82f6',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite',
                      margin: '0 auto 16px'
                    }} />
                    <p style={{ color: '#6b7280' }}>正在加载 search.jd.com...</p>
                  </div>
                </div>
              )}

              {/* 模拟的搜索界面 */}
              {(currentStep === 'clicking-search' || currentStep === 'typing-search') && (
                <div style={{
                  width: '100%',
                  height: '100%',
                  backgroundColor: 'white',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '40px'
                }}>
                  <div style={{ marginBottom: '40px' }}>
                    <h1 style={{
                      fontSize: '48px',
                      color: '#e60012',
                      fontWeight: 'bold',
                      margin: '0 0 16px 0'
                    }}>
                      京东(JD.COM)
                    </h1>
                    <p style={{
                      color: '#666',
                      fontSize: '16px',
                      margin: 0
                    }}>
                      正品低价、品质保障、配送及时、轻松购物！
                    </p>
                  </div>

                  <div style={{
                    width: '100%',
                    maxWidth: '600px',
                    position: 'relative'
                  }}>
                    <input
                      type="text"
                      value={typedSearch}
                      readOnly
                      placeholder="搜索商品"
                      style={{
                        width: '100%',
                        height: '48px',
                        padding: '0 60px 0 16px',
                        fontSize: '16px',
                        border: '2px solid #e60012',
                        borderRadius: '24px',
                        outline: 'none',
                        backgroundColor: currentStep === 'clicking-search' ? '#fff5f5' : 'white'
                      }}
                    />
                    <button style={{
                      position: 'absolute',
                      right: '4px',
                      top: '4px',
                      width: '40px',
                      height: '40px',
                      backgroundColor: '#e60012',
                      border: 'none',
                      borderRadius: '20px',
                      color: 'white',
                      fontSize: '16px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      🔍
                    </button>
                    {currentStep === 'typing-search' && showCursor && (
                      <div style={{
                        position: 'absolute',
                        left: `${16 + typedSearch.length * 9}px`,
                        top: '16px',
                        width: '1px',
                        height: '16px',
                        backgroundColor: '#333',
                        animation: 'blink 1s infinite'
                      }} />
                    )}
                  </div>
                </div>
              )}

              {/* 真实的京东页面 */}
              {(currentStep === 'jd-loaded' || currentStep === 'search-results' ||
                currentStep === 'scrolling-page') && (
                <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                  {/* 加载遮罩 */}
                  {!iframeLoaded && !iframeError && (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      zIndex: 10
                    }}>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{
                          width: '32px',
                          height: '32px',
                          border: '2px solid #3b82f6',
                          borderTop: '2px solid transparent',
                          borderRadius: '50%',
                          animation: 'spin 1s linear infinite',
                          margin: '0 auto 16px'
                        }} />
                        <p style={{ color: '#6b7280' }}>正在加载京东网站...</p>
                      </div>
                    </div>
                  )}

                  {/* 错误状态 */}
                  {iframeError && (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      zIndex: 10
                    }}>
                      <div style={{ textAlign: 'center', padding: '20px' }}>
                        <div style={{ fontSize: '48px', marginBottom: '16px' }}>🌐</div>
                        <h3 style={{ color: '#374151', marginBottom: '8px' }}>京东网站</h3>
                        <p style={{ color: '#6b7280', marginBottom: '16px' }}>
                          {currentStep === 'search-results' ? '搜索结果：Mac电脑' : '京东首页'}
                        </p>
                        <div style={{
                          backgroundColor: '#f3f4f6',
                          padding: '16px',
                          borderRadius: '8px',
                          border: '1px solid #e5e7eb'
                        }}>
                          <p style={{ color: '#6b7280', fontSize: '14px' }}>
                            模拟真实网站内容
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  <iframe
                    src={
                      currentStep === 'search-results'
                        ? 'https://search.jd.com/Search?keyword=Mac%E7%94%B5%E8%84%91&enc=utf-8'
                        : 'https://search.jd.com/'
                    }
                    style={{
                      width: '100%',
                      height: '100%',
                      border: 'none',
                      opacity: iframeLoaded && !iframeError ? 1 : 0
                    }}
                    title="京东网站"
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
                    loading="lazy"
                    onLoad={() => {
                      console.log('📱 Iframe loaded successfully');
                      setIframeLoaded(true);
                      setIframeError(false);
                    }}
                    onError={() => {
                      console.log('❌ Iframe failed to load');
                      setIframeError(true);
                      setIframeLoaded(false);
                    }}
                  />
                </div>
              )}

              {/* 商品页面 */}
              {(currentStep === 'clicking-product' || currentStep === 'product-page' ||
                currentStep === 'copying-title') && (
                <div style={{
                  width: '100%',
                  height: '100%',
                  backgroundColor: 'white',
                  display: 'flex',
                  flexDirection: 'column',
                  padding: '20px',
                  overflow: 'auto'
                }}>
                  {/* 商品页面头部 */}
                  <div style={{
                    borderBottom: '1px solid #e5e7eb',
                    paddingBottom: '16px',
                    marginBottom: '20px'
                  }}>
                    <div style={{
                      fontSize: '14px',
                      color: '#6b7280',
                      marginBottom: '8px'
                    }}>
                      京东 &gt; 电脑办公 &gt; 笔记本 &gt; 游戏本
                    </div>
                  </div>

                  {/* 商品信息 */}
                  <div style={{
                    display: 'flex',
                    gap: '30px',
                    marginBottom: '30px'
                  }}>
                    {/* 商品图片 */}
                    <div style={{
                      width: '400px',
                      height: '400px',
                      backgroundColor: '#f3f4f6',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '48px'
                    }}>
                      💻
                    </div>

                    {/* 商品详情 */}
                    <div style={{ flex: 1 }}>
                      <h1 style={{
                        fontSize: '24px',
                        fontWeight: 'bold',
                        color: '#1f2937',
                        marginBottom: '16px',
                        lineHeight: '1.4',
                        backgroundColor: currentStep === 'copying-title' ? '#fef3c7' : 'transparent',
                        padding: currentStep === 'copying-title' ? '8px' : '0',
                        borderRadius: currentStep === 'copying-title' ? '4px' : '0',
                        border: currentStep === 'copying-title' ? '2px dashed #f59e0b' : 'none',
                        position: 'relative'
                      }}>
                        {copiedTitle || 'Apple MacBook Pro 14英寸 M3芯片 8核CPU 10核GPU 8GB统一内存 512GB固态硬盘 深空灰色 笔记本电脑 MR7J3CH/A'}
                        {currentStep === 'copying-title' && (
                          <div style={{
                            position: 'absolute',
                            top: '-10px',
                            right: '-10px',
                            backgroundColor: '#f59e0b',
                            color: 'white',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            fontWeight: 'normal'
                          }}>
                            正在复制...
                          </div>
                        )}
                      </h1>

                      <div style={{
                        fontSize: '32px',
                        color: '#e60012',
                        fontWeight: 'bold',
                        marginBottom: '16px'
                      }}>
                        ¥14,999.00
                      </div>

                      <div style={{
                        fontSize: '14px',
                        color: '#6b7280',
                        marginBottom: '20px'
                      }}>
                        已有 <span style={{ color: '#e60012' }}>2.5万+</span> 人评价
                      </div>

                      <div style={{
                        display: 'flex',
                        gap: '12px'
                      }}>
                        <button style={{
                          backgroundColor: '#e60012',
                          color: 'white',
                          padding: '12px 24px',
                          border: 'none',
                          borderRadius: '4px',
                          fontSize: '16px',
                          cursor: 'pointer'
                        }}>
                          立即购买
                        </button>
                        <button style={{
                          backgroundColor: '#ff6600',
                          color: 'white',
                          padding: '12px 24px',
                          border: 'none',
                          borderRadius: '4px',
                          fontSize: '16px',
                          cursor: 'pointer'
                        }}>
                          加入购物车
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* 复制成功提示 */}
                  {currentStep === 'copying-title' && copiedTitle && (
                    <div style={{
                      position: 'fixed',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      backgroundColor: '#10b981',
                      color: 'white',
                      padding: '16px 24px',
                      borderRadius: '8px',
                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                      zIndex: 1000,
                      maxWidth: '80%',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
                        <span style={{ fontSize: '16px' }}>✅</span>
                        <div>
                          <div style={{ marginBottom: '4px' }}>已复制到剪贴板:</div>
                          <div style={{
                            fontSize: '12px',
                            opacity: 0.9,
                            maxHeight: '60px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                          }}>
                            {copiedTitle}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <FloatingWindow
        isVisible={isVisible}
        isRecordingMode={isRecordingMode}
        onClose={hideWindow}
        onMinimize={hideWindow}
        onMaximize={showWindow}
      >
        {isRecordingMode ? (
          <RecordingPanel
            recordingTime={recordingTime}
            isRecording={isRecording}
            onStop={stopRecording}
            onTogglePause={togglePause}
          />
        ) : (
          <>
            <TaskInput
              value={inputValue}
              onChange={setInputValue}
              onCameraClick={startRecording}
              onMicClick={() => console.log('Mic clicked')}
            />
            <ActionPanel onRun={handleRunClick} />
          </>
        )}
      </FloatingWindow>

      {/* 操作记录窗口 */}
      <ActionLog
        actions={actions}
        isVisible={isRecordingMode}
      />
    </div>
  );
}

export default App;