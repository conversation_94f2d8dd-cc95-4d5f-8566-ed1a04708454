/**
 * 录制操作类型
 */
export type ActionType = 'Left Click' | 'Right Click' | 'Type' | 'Scroll' | 'Press' | 'Copy';

/**
 * 录制操作数据结构
 */
export interface RecordingAction {
  id: string;
  type: ActionType;
  target: string;
  details?: string;
  timestamp: number;
}

/**
 * 生成测试数据：模拟用户在Chrome中搜索"Mac电脑"的操作
 */
export const generateTestActions = (): RecordingAction[] => {
  const baseTime = Date.now();
  
  return [
    {
      id: '1',
      type: 'Left Click',
      target: 'Chrome',
      timestamp: baseTime
    },
    {
      id: '2',
      type: 'Left Click',
      target: '<class_id="address-bar">',
      timestamp: baseTime + 1500
    },
    {
      id: '3',
      type: 'Type',
      target: 'search.jd.com',
      timestamp: baseTime + 2000
    },
    {
      id: '4',
      type: 'Press',
      target: 'Enter',
      timestamp: baseTime + 3500
    },
    {
      id: '5',
      type: 'Left Click',
      target: '<class_id="search-input">',
      timestamp: baseTime + 6000
    },
    {
      id: '6',
      type: 'Type',
      target: 'Mac电脑',
      timestamp: baseTime + 6500
    },
    {
      id: '7',
      type: 'Left Click',
      target: '<class_id="search-button">',
      timestamp: baseTime + 8000
    },
    {
      id: '8',
      type: 'Scroll',
      target: 'Page',
      details: 'Down 3 units',
      timestamp: baseTime + 10000
    },
    {
      id: '9',
      type: 'Left Click',
      target: '<class_id="_goodsContainer_1_plugin_goodsContainer">',
      details: 'Click first product container',
      timestamp: baseTime + 12000
    },
    {
      id: '10',
      type: 'Copy',
      target: 'Product Title',
      details: 'Copy product title',
      timestamp: baseTime + 14000
    }
  ];
};
